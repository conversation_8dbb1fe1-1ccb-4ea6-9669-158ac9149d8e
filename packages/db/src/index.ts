import { neon } from "@neondatabase/serverless";
import type { Env } from "@repo/config";
import { getDatabaseUrl } from "@repo/config";
import { drizzle } from "drizzle-orm/neon-http";

// Optimized configuration for Cloudflare Workers
const NEON_CONFIG = {
  // Use HTTP fetch instead of WebSocket for better Cloudflare Workers compatibility
  fetchConnectionCache: true,
  // Enable array mode for better performance with large result sets
  arrayMode: false,
  // Enable full results for better debugging in development
  fullResults: false,
  // Connection timeout optimized for edge functions
  fetchOptions: {
    cache: 'no-store' as const,
  },
} as const;

export function createDatabase(databaseUrl: string, options?: {
  arrayMode?: boolean;
  fullResults?: boolean;
  debug?: boolean;
}) {
  const config = {
    ...NEON_CONFIG,
    ...options,
  };

  const sql = neon(databaseUrl, config);
  return drizzle(sql, {
    logger: config.debug,
  });
}

export function createDatabaseFromEnv(
  env: Env,
  options?: {
    arrayMode?: boolean;
    fullResults?: boolean;
  }
) {
  // Get the appropriate database URL based on environment
  const databaseUrl = getDatabaseUrl(env);

  // Enable debug mode in development
  const debug = env.NODE_ENV === "development";

  return createDatabase(databaseUrl, {
    debug,
    ...options,
  });
}

// Export Drizzle ORM types and operators for direct usage
export type { InferInsertModel, InferSelectModel } from "drizzle-orm";
export {
  // Comparison operators
  eq, ne, gt, gte, lt, lte,
  // Logical operators
  and, or, not,
  // Pattern matching
  like, ilike, notLike, notIlike,
  // Array operators
  inArray, notInArray,
  // Null checks
  isNull, isNotNull,
  // Text operators
  exists, notExists,
  // Aggregate functions
  count, sum, avg, min, max,
  // SQL functions
  sql,
  // Placeholder for prepared statements
  placeholder,
} from "drizzle-orm";

// Export schema and utilities
export * from "./schema/index.js";
export * from "./utils/auth-context.js";
export * from "./utils/permissions.js";
