{"name": "@repo/db", "version": "0.1.0", "type": "module", "private": true, "exports": {".": "./dist/index.js", "./schema": "./dist/schema/index.js", "./migrations": "./dist/migrations/index.js"}, "types": "./dist/index.d.ts", "scripts": {"dev": "tsc --watch", "build": "tsc", "check-types": "tsc --noEmit", "lint": "biome check .", "lint:fix": "biome check --write .", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:studio": "drizzle-kit studio"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "drizzle-orm": "^0.37.0", "@repo/shared": "workspace:*", "@repo/config": "workspace:*"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.3", "drizzle-kit": "^0.29.1", "typescript": "5.8.2"}}