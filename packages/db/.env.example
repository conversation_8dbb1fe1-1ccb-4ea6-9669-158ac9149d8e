# Neon Database Configuration
# You can use either a single DATABASE_URL or separate URLs for different environments

# Option 1: Single database URL (will be used for all environments)
DATABASE_URL=postgresql://username:<EMAIL>/neondb

# Option 2: Environment-specific URLs (recommended)
# Development branch URL
DATABASE_URL_DEV=postgresql://username:<EMAIL>/neondb?options=project%3Dep-example-123456

# Production branch URL  
DATABASE_URL_PROD=postgresql://username:<EMAIL>/neondb?options=project%3Dep-example-789012

# Environment
NODE_ENV=development

# Auth Configuration
BETTER_AUTH_SECRET=your-32-character-secret-key-here
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration
RESEND_API_KEY=your-resend-api-key

# Cloudflare R2 Storage
R2_ACCESS_KEY_ID=your-r2-access-key
R2_SECRET_ACCESS_KEY=your-r2-secret-key
R2_BUCKET_NAME=your-bucket-name
