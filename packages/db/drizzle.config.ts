import { defineConfig } from "drizzle-kit";
import { getEnv, getDatabaseUrl } from "@repo/config";

// Load environment variables
const env = getEnv();
const databaseUrl = getDatabaseUrl(env);

export default defineConfig({
  // Database connection
  dialect: "postgresql",
  dbCredentials: {
    url: databaseUrl,
  },
  
  // Schema and migrations
  schema: "./src/schema/index.ts",
  out: "./src/migrations",
  
  // Drizzle Studio configuration
  introspect: {
    casing: "snake_case",
  },
  
  // Migration configuration
  migrations: {
    prefix: "timestamp",
    table: "__drizzle_migrations__",
    schema: "public",
  },
  
  // Development options
  verbose: env.NODE_ENV === "development",
  strict: true,
});
