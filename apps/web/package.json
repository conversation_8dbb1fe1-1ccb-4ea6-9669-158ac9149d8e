{"name": "web", "version": "0.1.0", "type": "module", "private": true, "scripts": {"dev": "remix vite:dev --port 5173", "build": "remix vite:build", "preview": "wrangler dev --port 3000", "deploy": "wrangler deploy", "lint": "biome check .", "lint:fix": "biome check --write .", "check-types": "tsc --noEmit"}, "dependencies": {"@remix-run/cloudflare": "^2.16.8", "@remix-run/react": "^2.16.8", "@repo/analytics": "workspace:*", "@repo/auth": "workspace:*", "@repo/config": "workspace:*", "@repo/db": "workspace:*", "@repo/shared": "workspace:*", "@repo/ui-kit": "workspace:*", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "isbot": "^5.1.28", "lucide-react": "^0.468.0", "react": "^18.3.1", "react-dom": "^18.3.1", "tailwind-merge": "^2.5.4"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@remix-run/dev": "^2.16.8", "@tailwindcss/vite": "^4.0.0", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "tailwindcss": "^4.0.0", "typescript": "5.8.2", "vite": "^5.4.19", "wrangler": "^4.22.0"}}